import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../domain/entities/transaction.dart';
import '../../../../domain/entities/transaction_item_with_details.dart';
import '../../../providers/unpaid_transactions_provider.dart';
import '../../../widgets/loading_indicator.dart';
import '../../../widgets/confirmation_dialog.dart';
import 'transaction_items_list.dart';
import 'edit_transaction_bottom_sheet.dart';

class UnpaidTransactionCard extends ConsumerStatefulWidget {
  final TransactionEntity transaction;
  final List<TransactionItemWithDetailsEntity>? unpaidItems;
  final double? totalUnpaidAmount;

  const UnpaidTransactionCard({
    super.key,
    required this.transaction,
    this.unpaidItems,
    this.totalUnpaidAmount,
  });

  @override
  ConsumerState<UnpaidTransactionCard> createState() => _UnpaidTransactionCardState();
}

class _UnpaidTransactionCardState extends ConsumerState<UnpaidTransactionCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final transaction = widget.transaction;
    final unpaidItems = widget.unpaidItems;
    final totalUnpaidAmount = widget.totalUnpaidAmount ?? transaction.remainingAmount;
    final isPartiallyPaid = transaction.status == AppConstants.statusPartiallyPaid;

    return GestureDetector(
      onLongPress: () => _showOptionsBottomSheet(context, ref, transaction),
      child: Card(
        margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
        child: Column(
          children: [
            ListTile(
              contentPadding: const EdgeInsets.all(AppConstants.defaultPadding),
            title: Row(
              children: [
                Text(
                  Formatters.formatDate(transaction.date),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isPartiallyPaid
                        ? Theme.of(context).colorScheme.secondary.withAlpha(51)
                        : Theme.of(context).colorScheme.error.withAlpha(51),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    // Display "Paid" instead of "Paid_" for better UI
                    transaction.status == AppConstants.statusPaid ? 'Paid' : transaction.status,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isPartiallyPaid
                              ? Theme.of(context).colorScheme.secondary
                              : Theme.of(context).colorScheme.error,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: AppConstants.smallPadding),
                Row(
                  children: [
                    Text(
                      '${AppConstants.labelTotalAmount}: ',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      Formatters.formatCurrency(transaction.totalAmount),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '${AppConstants.labelRemainingAmount}: ',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      Formatters.formatCurrency(totalUnpaidAmount),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.error,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                if (transaction.totalAmount != totalUnpaidAmount) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        'Paid so far: ',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        Formatters.formatCurrency(transaction.totalAmount - totalUnpaidAmount),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
            trailing: IconButton(
              icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
              onPressed: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
            ),
          ),
          if (_isExpanded)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Divider(),
                  Text(
                    'Unpaid Items',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  // If we have unpaid items directly, use them; otherwise fetch from provider
                  unpaidItems != null
                      ? TransactionItemsList(items: unpaidItems)
                      : Consumer(
                          builder: (context, ref, child) {
                            final transactionWithItemsAsyncValue = ref.watch(
                              transactionWithItemsProvider(transaction.id),
                            );

                            return transactionWithItemsAsyncValue.when(
                              data: (transactionWithItems) {
                                // Filter to show only unpaid items
                                final unpaidItemsFromProvider = transactionWithItems.items
                                    .where((item) => item.transactionItem.remainingAmount > 0)
                                    .toList();

                                return TransactionItemsList(
                                  items: unpaidItemsFromProvider,
                                );
                              },
                              loading: () => const SizedBox(
                                height: 100,
                                child: Center(
                                  child: LoadingIndicator(),
                                ),
                              ),
                              error: (error, stackTrace) => Text(
                                'Error loading items: $error',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context).colorScheme.error,
                                    ),
                              ),
                            );
                          },
                        ),
                  const SizedBox(height: AppConstants.defaultPadding),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showOptionsBottomSheet(BuildContext context, WidgetRef ref, TransactionEntity transaction) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.bottomSheetBorderRadius),
        ),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Transaction Options',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Transaction'),
              onTap: () async {
                Navigator.of(context).pop();
                await _showEditTransactionBottomSheet(context, ref);
              },
            ),
            ListTile(
              leading: Icon(Icons.delete, color: Theme.of(context).colorScheme.error),
              title: Text(
                'Delete Transaction',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
              onTap: () async {
                Navigator.of(context).pop();
                await _confirmDeleteTransaction(context, ref);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showEditTransactionBottomSheet(BuildContext context, WidgetRef ref) async {
    final result = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.bottomSheetBorderRadius),
        ),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: EditTransactionBottomSheet(transaction: widget.transaction),
      ),
    );

    // Refresh the unpaid transactions if the edit was successful
    if (result == true && context.mounted) {
      ref.read(unpaidTransactionsProvider.notifier).refreshUnpaidTransactions();
    }
  }

  Future<void> _confirmDeleteTransaction(BuildContext context, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: const Text('Are you sure you want to delete this transaction? This action cannot be undone and will remove all associated items and payments.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(AppConstants.buttonCancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text(AppConstants.buttonDelete),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      try {
        final deleteTransactionUseCase = ref.read(deleteTransactionUseCaseProvider);
        await deleteTransactionUseCase(widget.transaction.id);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Transaction deleted successfully')),
          );
          // Refresh the unpaid transactions
          ref.read(unpaidTransactionsProvider.notifier).refreshUnpaidTransactions();
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting transaction: $e')),
          );
        }
      }
    }
  }
}
