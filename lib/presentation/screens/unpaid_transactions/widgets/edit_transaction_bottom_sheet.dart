import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../core/utils/validators.dart';
import '../../../../domain/entities/transaction.dart';
import '../../../providers/providers.dart';
import '../../../widgets/custom_text_field.dart';

class EditTransactionBottomSheet extends ConsumerStatefulWidget {
  final TransactionEntity transaction;

  const EditTransactionBottomSheet({
    super.key,
    required this.transaction,
  });

  @override
  ConsumerState<EditTransactionBottomSheet> createState() => _EditTransactionBottomSheetState();
}

class _EditTransactionBottomSheetState extends ConsumerState<EditTransactionBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _dateController;
  bool _isLoading = false;
  late DateTime _selectedDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.transaction.date;
    _dateController = TextEditingController(
      text: Formatters.formatDate(_selectedDate),
    );
  }

  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = Formatters.formatDate(picked);
      });
    }
  }

  Future<void> _updateTransaction() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedTransaction = widget.transaction.copyWith(
        date: _selectedDate,
        updatedAt: DateTime.now(),
      );

      final updateTransactionUseCase = ref.read(updateTransactionUseCaseProvider);
      final success = await updateTransactionUseCase(updatedTransaction);

      setState(() {
        _isLoading = false;
      });

      if (success && mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Transaction updated successfully')),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to update transaction')),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating transaction: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Edit Transaction',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            CustomTextField(
              controller: _dateController,
              label: AppConstants.labelDate,
              readOnly: true,
              onTap: _selectDate,
              suffixIcon: const Icon(Icons.calendar_today),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.errorRequiredField;
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'Total Amount: ${Formatters.formatCurrency(widget.transaction.totalAmount)}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'Remaining Amount: ${Formatters.formatCurrency(widget.transaction.remainingAmount)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: _isLoading ? null : _updateTransaction,
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(AppConstants.buttonUpdate),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(AppConstants.buttonCancel),
            ),
          ],
        ),
      ),
    );
  }
}
