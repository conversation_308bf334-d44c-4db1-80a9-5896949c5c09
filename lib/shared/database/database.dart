import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:beta/core/constants/app_constants.dart';

import 'tables.dart';

part 'database.g.dart';

@DriftDatabase(tables: [Items, Transactions, TransactionItems, Payments, PaidItems])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => AppConstants.databaseVersion;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) {
        return m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        if (from < 2) {
          // Add remainingAmount column to transaction_items table
          await m.addColumn(transactionItems, transactionItems.remainingAmount);

          // Initialize remainingAmount for existing transaction items
          // Since we're assuming no existing data, this is just for completeness
          await customStatement('''
            UPDATE transaction_items
            SET remaining_amount = quantity * price_at_purchase
            WHERE remaining_amount IS NULL
          ''');
        }

        if (from < 3) {
          // Create paid_items table if it doesn't exist
          await m.createTable(paidItems);
        }
      },
    );
  }

  // Items operations
  Future<List<Item>> getAllItems() => select(items).get();

  Future<List<Item>> searchItems(String query) {
    return (select(items)..where((item) => item.name.like('%$query%'))).get();
  }

  Future<Item> getItemById(int id) {
    return (select(items)..where((item) => item.id.equals(id))).getSingle();
  }

  Future<bool> isItemNameExists(String name) async {
    final query = select(items)..where((item) => item.name.equals(name));
    final result = await query.get();
    return result.isNotEmpty;
  }

  Future<int> addItem(ItemsCompanion item) {
    return into(items).insert(item);
  }

  Future<bool> updateItem(ItemsCompanion item) {
    return update(items).replace(item);
  }

  Future<int> deleteItem(int id) {
    return (delete(items)..where((item) => item.id.equals(id))).go();
  }

  // Transactions operations
  Future<List<Transaction>> getAllTransactions() => select(transactions).get();

  Future<List<Transaction>> getUnpaidTransactions() {
    return (select(transactions)
          ..where((t) => t.status.isIn([AppConstants.statusUnpaid, AppConstants.statusPartiallyPaid]))
          ..orderBy([(t) => OrderingTerm.desc(t.date)]))
        .get();
  }

  Future<List<Transaction>> getPaidTransactions() {
    return (select(transactions)
          ..where((t) => t.status.equals(AppConstants.statusPaid))
          ..orderBy([(t) => OrderingTerm.desc(t.date)]))
        .get();
  }

  Future<Transaction> getTransactionById(int id) {
    return (select(transactions)..where((t) => t.id.equals(id))).getSingle();
  }

  Future<int> addTransaction(TransactionsCompanion transaction) {
    return into(transactions).insert(transaction);
  }

  Future<bool> updateTransaction(TransactionsCompanion transaction) {
    return update(transactions).replace(transaction);
  }

  // Transaction items operations
  Future<List<TransactionItem>> getTransactionItems(int transactionId) {
    return (select(transactionItems)
          ..where((ti) => ti.transactionId.equals(transactionId)))
        .get();
  }

  Future<int> addTransactionItem(TransactionItemsCompanion transactionItem) {
    return into(transactionItems).insert(transactionItem);
  }

  // Payments operations
  Future<List<Payment>> getTransactionPayments(int transactionId) {
    return (select(payments)
          ..where((p) => p.transactionId.equals(transactionId))
          ..orderBy([(p) => OrderingTerm.desc(p.date)]))
        .get();
  }

  Future<int> addPayment(PaymentsCompanion payment) {
    return into(payments).insert(payment);
  }

  // Paid Items operations
  Future<List<PaidItem>> getPaymentPaidItems(int paymentId) {
    return (select(paidItems)
          ..where((pi) => pi.paymentId.equals(paymentId)))
        .get();
  }

  Future<int> addPaidItem(PaidItemsCompanion paidItem) {
    return into(paidItems).insert(paidItem);
  }

  Future<List<PaidItem>> getAllPaidItems() {
    return select(paidItems).get();
  }

  // Get all paid items for a specific transaction item
  Future<List<PaidItem>> getPaidItemsForTransactionItem(int transactionItemId) {
    return (select(paidItems)
          ..where((pi) => pi.transactionItemId.equals(transactionItemId)))
        .get();
  }

  // Get all unpaid transaction items (items with remainingAmount > 0)
  Future<List<TransactionItemWithDetails>> getUnpaidTransactionItems() async {
    final query = select(transactionItems).join([
      leftOuterJoin(items, items.id.equalsExp(transactionItems.itemId)),
      leftOuterJoin(transactions, transactions.id.equalsExp(transactionItems.transactionId)),
    ])
      ..where(transactionItems.remainingAmount.isBiggerThanValue(0))
      ..orderBy([OrderingTerm.desc(transactions.date)]);

    final results = await query.get();
    return results.map((row) {
      return TransactionItemWithDetails(
        transactionItem: row.readTable(transactionItems),
        item: row.readTable(items),
      );
    }).toList();
  }

  // Update remaining amount for a transaction item
  Future<void> updateTransactionItemRemainingAmount(int transactionItemId, double newRemainingAmount) {
    return (update(transactionItems)..where((ti) => ti.id.equals(transactionItemId)))
        .write(TransactionItemsCompanion(
      remainingAmount: Value(newRemainingAmount),
    ));
  }

  // Calculate and update transaction remaining amount based on its items
  Future<void> recalculateTransactionRemainingAmount(int transactionId) async {
    // Get all transaction items for this transaction
    final transactionItemsList = await (select(transactionItems)
          ..where((ti) => ti.transactionId.equals(transactionId)))
        .get();

    // Calculate total remaining amount
    final totalRemainingAmount = transactionItemsList.fold(
      0.0,
      (sum, item) => sum + item.remainingAmount,
    );

    // Determine new status
    String newStatus;
    if (totalRemainingAmount <= 0) {
      newStatus = AppConstants.statusPaid;
    } else {
      // Check if any amount has been paid
      final totalOriginalAmount = transactionItemsList.fold(
        0.0,
        (sum, item) => sum + (item.quantity * item.priceAtPurchase),
      );

      if (totalRemainingAmount < totalOriginalAmount) {
        newStatus = AppConstants.statusPartiallyPaid;
      } else {
        newStatus = AppConstants.statusUnpaid;
      }
    }

    // Update transaction
    await (update(transactions)..where((t) => t.id.equals(transactionId)))
        .write(TransactionsCompanion(
      remainingAmount: Value(totalRemainingAmount),
      status: Value(newStatus),
      updatedAt: Value(DateTime.now()),
    ));
  }

  // Transaction with items
  Future<TransactionWithItems> getTransactionWithItems(int transactionId) async {
    final transaction = await getTransactionById(transactionId);
    final transactionItemsList = await getTransactionItems(transactionId);

    final transactionItemsWithDetails = <TransactionItemWithDetails>[];

    for (final transactionItem in transactionItemsList) {
      final item = await getItemById(transactionItem.itemId);
      transactionItemsWithDetails.add(
        TransactionItemWithDetails(
          transactionItem: transactionItem,
          item: item,
        ),
      );
    }

    return TransactionWithItems(
      transaction: transaction,
      items: transactionItemsWithDetails,
    );
  }

  // Transaction with payments
  Future<TransactionWithPayments> getTransactionWithPayments(int transactionId) async {
    final transaction = await getTransactionById(transactionId);
    final paymentsList = await getTransactionPayments(transactionId);

    return TransactionWithPayments(
      transaction: transaction,
      payments: paymentsList,
    );
  }

  // Complete transaction details
  Future<CompleteTransactionDetails> getCompleteTransactionDetails(int transactionId) async {
    final transaction = await getTransactionById(transactionId);
    final transactionItemsList = await getTransactionItems(transactionId);
    final paymentsList = await getTransactionPayments(transactionId);

    final transactionItemsWithDetails = <TransactionItemWithDetails>[];

    for (final transactionItem in transactionItemsList) {
      final item = await getItemById(transactionItem.itemId);
      transactionItemsWithDetails.add(
        TransactionItemWithDetails(
          transactionItem: transactionItem,
          item: item,
        ),
      );
    }

    return CompleteTransactionDetails(
      transaction: transaction,
      items: transactionItemsWithDetails,
      payments: paymentsList,
    );
  }

  // Create transaction with items
  Future<int> createTransactionWithItems(
    TransactionsCompanion transactionData,
    List<TransactionItemsCompanion> items,
  ) async {
    return await transaction(() async {
      final transactionId = await into(transactions).insert(transactionData);

      for (final item in items) {
        final updatedItem = item.copyWith(
          transactionId: Value(transactionId),
        );
        await into(transactionItems).insert(updatedItem);
      }

      return transactionId;
    });
  }

  // Add payment and update transaction
  Future<int> addPaymentAndUpdateTransaction(
    PaymentsCompanion payment,
    TransactionsCompanion updatedTransaction,
  ) async {
    return await transaction(() async {
      final paymentId = await into(payments).insert(payment);
      await update(transactions).replace(updatedTransaction);
      return paymentId;
    });
  }

  // Add payment with paid items and update transaction
  Future<int> addPaymentWithPaidItemsAndUpdateTransaction(
    PaymentsCompanion payment,
    TransactionsCompanion updatedTransaction,
    List<PaidItemsCompanion> paidItemsList,
  ) async {
    return await transaction(() async {
      final paymentId = await into(payments).insert(payment);

      // Update the payment ID for each paid item
      for (final paidItem in paidItemsList) {
        final updatedPaidItem = paidItem.copyWith(
          paymentId: Value(paymentId),
        );
        await into(paidItems).insert(updatedPaidItem);
      }

      await update(transactions).replace(updatedTransaction);
      return paymentId;
    });
  }
}

// Custom data classes
class TransactionItemWithDetails {
  final TransactionItem transactionItem;
  final Item item;

  TransactionItemWithDetails({
    required this.transactionItem,
    required this.item,
  });
}

class TransactionWithItems {
  final Transaction transaction;
  final List<TransactionItemWithDetails> items;

  TransactionWithItems({
    required this.transaction,
    required this.items,
  });
}

class TransactionWithPayments {
  final Transaction transaction;
  final List<Payment> payments;

  TransactionWithPayments({
    required this.transaction,
    required this.payments,
  });
}

class CompleteTransactionDetails {
  final Transaction transaction;
  final List<TransactionItemWithDetails> items;
  final List<Payment> payments;

  CompleteTransactionDetails({
    required this.transaction,
    required this.items,
    required this.payments,
  });
}

// Database connection
LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, AppConstants.databaseName));
    return NativeDatabase.createInBackground(file);
  });
}
